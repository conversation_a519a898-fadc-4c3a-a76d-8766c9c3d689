<!DOCTYPE html> <!-- 1. Декларация: това е HTML5 документ -->
<html lang="en"> <!-- 2. Начало на HTML, езикът е зададен като английски -->
<head> <!-- 3. Начало на секцията с мета-информация и стилове -->
    <meta charset="UTF-8"> <!-- 4. Задава кодировката на документа като UTF-8 -->
    <meta name="viewport" content="width=device-width, initial-scale=1.0"> <!-- 5. Прави страницата адаптивна за мобилни устройства -->
    <title>Simple Weather App</title> <!-- 6. Заглавие на страницата, което се показва в таба на браузъра -->
    <!-- 7. Начало на CSS стила вграден в документа -->
    <style>
        body { /* 8. Стилове за елемента body (цялата страница) */
            font-family: Arial, sans-serif; /* 9. Задава шрифт Arial или безсерифен ако Arial липсва */
            background-color: lightblue; /* 10. Задава светлосин фон на страницата */
            margin: 0; /* 11. Премахва подразбиращите се отстояния на body */
            padding: 20px; /* 12. Добавя вътрешно отстояние около съдържанието */
        } /* 13. Край на блока за body */

        .weather-app { /* 14. Стилове за основния контейнер на приложението */
            background-color: white; /* 15. Бял фон */
            max-width: 500px; /* 16. Максимална ширина 500px */
            margin: 0 auto; /* 17. Центрира контейнера хоризонтално */
            padding: 20px; /* 18. Вътрешно отстояние вътре в картата */
            border-radius: 10px; /* 19. Заобля ъглите на картата */
            box-shadow: 0 4px 8px gray; /* 20. Добавя сянка за дълбочина */
        } /* 21. Край на .weather-app */

        .country-selector { /* 22. Стил за секцията с избор на държава */
            text-align: center; /* 23. Центрира съдържанието */
            margin-bottom: 20px; /* 24. Отстояние под селектора */
        } /* 25. Край на .country-selector */

        .country-dropdown { /* 26. Стилове за падащото меню */
            background-color: lightgray; /* 27. Светлосив фон */
            border: 2px solid gray; /* 28. Сива рамка */
            padding: 10px 20px; /* 29. Вътрешно отстояние (вертикално и хоризонтално) */
            border-radius: 5px; /* 30. Заобляне на ъглите */
            font-size: 16px; /* 31. Големина на шрифта */
            cursor: pointer; /* 32. Промяна на курсора при задържане (рука) */
        } /* 33. Край на .country-dropdown */

        .header { /* 34. Стил за заглавната част с времето */
            text-align: center; /* 35. Центрирано подравняване */
            margin-bottom: 20px; /* 36. Разстояние под хедъра */
        } /* 37. Край на .header */

        .location { /* 38. Стил за текста с местоположение */
            font-size: 18px; /* 39. Размер на шрифта */
            margin-bottom: 10px; /* 40. Отстояние под него */
        } /* 41. Край на .location */

        .temperature { /* 42. Стил за температурата */
            font-size: 48px; /* 43. Голям размер на шрифта */
            font-weight: bold; /* 44. Удебелен текст */
            color: blue; /* 45. Син цвят */
            margin-bottom: 10px; /* 46. Отстояние под температурата */
        } /* 47. Край на .temperature */

        .description { /* 48. Стил за описанието на времето */
            font-size: 16px; /* 49. Размер на шрифта */
            color: gray; /* 50. Сив цвят */
        } /* 51. Край на .description */

        .details { /* 52. Контейнер за допълнителни детайли (вятър, влажност) */
            display: flex; /* 53. Използва flexbox за подредба */
            justify-content: space-around; /* 54. Разпределя пространството равномерно */
            margin: 20px 0; /* 55. Горен и долен марж */
        } /* 56. Край на .details */

        .detail-box { /* 57. Стил за отделна кутия с детайл */
            text-align: center; /* 58. Центрира съдържанието */
            background-color: lightgray; /* 59. Светлосив фон */
            padding: 15px; /* 60. Вътрешно отстояние */
            border-radius: 5px; /* 61. Заобляне на ъглите */
            width: 45%; /* 62. Задава ширина */
        } /* 63. Край на .detail-box */

        .detail-title { /* 64. Стил за заглавие на детайла */
            font-size: 14px; /* 65. Малък шрифт */
            color: gray; /* 66. Сив цвят */
            margin-bottom: 5px; /* 67. Отстояние под заглавието */
        } /* 68. Край на .detail-title */

        .detail-value { /* 69. Стил за стойността на детайла */
            font-size: 20px; /* 70. По-голям шрифт */
            font-weight: bold; /* 71. Удебелен */
            color: black; /* 72. Черен текст */
        } /* 73. Край на .detail-value */

        .forecast { /* 74. Контейнер за прогнозата */
            margin-top: 20px; /* 75. Отстояние отгоре */
        } /* 76. Край на .forecast */

        .forecast-title { /* 77. Заглавие на прогнозата */
            font-size: 18px; /* 78. Размер на шрифта */
            font-weight: bold; /* 79. Удебелен */
            margin-bottom: 15px; /* 80. Отстояние под заглавието */
            text-align: center; /* 81. Центриране */
        } /* 82. Край на .forecast-title */

        .forecast-items { /* 83. Контейнер за елементите от прогнозата */
            display: grid; /* 84. Използва CSS grid */
            grid-template-columns: repeat(4, 1fr); /* 85. 4 еднакви колони */
            gap: 10px; /* 86. Разстояние между елементите */
        } /* 87. Край на .forecast-items */

        .forecast-item { /* 88. Всеки отделен елемент от прогнозата */
            text-align: center; /* 89. Центриран текст */
            background-color: lightgray; /* 90. Светлосив фон */
            padding: 8px; /* 91. Вътрешно отстояние */
            border-radius: 5px; /* 92. Заобляне на ъглите */
        } /* 93. Край на .forecast-item */

        .forecast-time { /* 94. Стил за часа във всеки блок */
            font-size: 11px; /* 95. Много малък шрифт */
            color: gray; /* 96. Сив текст */
            margin-bottom: 3px; /* 97. Малко разстояние под него */
        } /* 98. Край на .forecast-time */

        .forecast-temp { /* 99. Стил за температурата във всеки блок */
            font-size: 14px; /* 100. Среден шрифт */
            font-weight: bold; /* 101. Удебелен */
            color: black; /* 102. Черен */
        } /* 103. Край на .forecast-temp */

        @media (max-width: 480px) { /* 104. Медия заявка за малки екрани (мобилни) */
            .forecast-items { /* 105. Промяна на оформлението при мобилни */
                grid-template-columns: repeat(2, 1fr); /* 106. 2 колони вместо 4 */
            } /* 107. Край на модифицирания .forecast-items */
        } /* 108. Край на @media */
    </style> <!-- 109. Край на CSS стила -->
</head> <!-- 110. Край на head секцията -->
<body> <!-- 111. Начало на body секцията -->
    <div class="weather-app"> <!-- 112. Основен контейнер на приложението -->

        <div class="country-selector"> <!-- 113. Контейнер за избор на държава -->
            <select class="country-dropdown" onchange="showWeather(this.value)"> <!-- 114. Падащо меню, при промяна извиква функция -->
                <option value="bulgaria">Bulgaria</option> <!-- 115. Опция: България -->
                <option value="uk">United Kingdom</option> <!-- 116. Опция: Великобритания -->
            </select> <!-- 117. Край на падащото меню -->
        </div> <!-- 118. Край на country-selector -->

        <div class="header"> <!-- 119. Заглавна част (локейшън, температура, описание) -->
            <div class="location" id="location">Sofia, Bulgaria</div> <!-- 120. Показва текущото местоположение -->
            <div class="temperature" id="temperature">22°</div> <!-- 121. Показва текущата температура -->
            <div class="description" id="description">Sunny</div> <!-- 122. Показва описание на времето -->
        </div> <!-- 123. Край на header -->

        <div class="details"> <!-- 124. Секция за допълнителни детайли -->
            <div class="detail-box"> <!-- 125. Първа кутия: скорост на вятъра -->
                <div class="detail-title">Wind Speed</div> <!-- 126. Заглавие: скорост на вятъра -->
                <div class="detail-value" id="windSpeed">15 km/h</div> <!-- 127. Стойност: 15 km/h -->
            </div> <!-- 128. Край на първата detail-box -->
            <div class="detail-box"> <!-- 129. Втора кутия: влажност -->
                <div class="detail-title">Humidity</div> <!-- 130. Заглавие: влажност -->
                <div class="detail-value" id="humidity">5%</div> <!-- 131. Стойност: 5% -->
            </div> <!-- 132. Край на втората detail-box -->
        </div> <!-- 133. Край на секцията details -->

        <div class="forecast"> <!-- 134. Секция за прогнозата -->
            <div class="forecast-title">Today's Forecast (Every 3 Hours)</div> <!-- 135. Заглавие на прогнозата -->
            <div class="forecast-items" id="forecastItems"> <!-- 136. Контейнер, където JS ще постави елементите -->
                <!-- 137. Тук ще се генерират прогнози чрез JavaScript -->
            </div> <!-- 138. Край на forecast-items -->
        </div> <!-- 139. Край на forecast секцията -->

    </div> <!-- 140. Край на контейнера weather-app -->

    <script> <!-- 141. Начало на JavaScript код -->
    // 1. Функция, която пита .NET API-то и обновява екрана
    async function showWeather(country) {
        // 2. Правим GET заявка към бекенда (порт 5240, HTTP)
        const response = await fetch(`http://localhost:5240/weather/${country}`);

        // 3. Ако няма резултат → показваме съобщение и спираме
        if (!response.ok) {
            alert("Няма данни за " + country);
            return;
        }

        // 4. Превръщаме отговорa (JSON) в JS обект
        const data = await response.json();

        /* 5. Обновяваме DOM елементите със свежите данни */
        document.getElementById('location').textContent    = data.location;
        document.getElementById('temperature').textContent = data.temperature;
        document.getElementById('description').textContent = data.description;
        document.getElementById('windSpeed').textContent   = data.windSpeed;
        document.getElementById('humidity').textContent    = data.humidity;

        // 6. Пълним прогнозата
        const forecastContainer = document.getElementById('forecastItems');
        forecastContainer.innerHTML = ''; // изчистване

        data.forecast.forEach(f => {
            const item = document.createElement('div');
            item.className = 'forecast-item';
            item.innerHTML = `
                <div class="forecast-time">${f.time}</div>
                <div class="forecast-temp">${f.temp}</div>
            `;
            forecastContainer.appendChild(item);
        });
    }


</body> <!-- 194. Край на body -->
</html> <!-- 195. Край на HTML документа -->
