<!DOCTYPE html> <!-- 1. Декларация: това е HTML5 документ -->
<html lang="en"> <!-- 2. Начало на HTML, езикът е зададен като английски -->
<head> <!-- 3. Начало на секцията с мета-информация и стилове -->
    <meta charset="UTF-8"> <!-- 4. Задава кодировката на документа като UTF-8 -->
    <meta name="viewport" content="width=device-width, initial-scale=1.0"> <!-- 5. Прави страницата адаптивна за мобилни устройства -->
    <title>Simple Weather App</title> <!-- 6. Заглавие на страницата, което се показва в таба на браузъра -->
    <!-- 7. Начало на CSS стила вграден в документа -->
    <style>
        body { /* 8. Стилове за елемента body (цялата страница) */
            font-family: Arial, sans-serif; /* 9. Задава шрифт Arial или безсерифен ако Arial липсва */
            background-color: lightblue; /* 10. Задава светлосин фон на страницата */
            margin: 0; /* 11. Премахва подразбиращите се отстояния на body */
            padding: 20px; /* 12. Добавя вътрешно отстояние около съдържанието */
        } /* 13. Край на блока за body */

        .weather-app { /* 14. Стилове за основния контейнер на приложението */
            background-color: white; /* 15. Бял фон */
            max-width: 500px; /* 16. Максимална ширина 500px */
            margin: 0 auto; /* 17. Центрира контейнера хоризонтално */
            padding: 20px; /* 18. Вътрешно отстояние вътре в картата */
            border-radius: 10px; /* 19. Заобля ъглите на картата */
            box-shadow: 0 4px 8px gray; /* 20. Добавя сянка за дълбочина */
        } /* 21. Край на .weather-app */

        .country-selector { /* 22. Стил за секцията с избор на държава */
            text-align: center; /* 23. Центрира съдържанието */
            margin-bottom: 20px; /* 24. Отстояние под селектора */
        } /* 25. Край на .country-selector */

        .country-dropdown { /* 26. Стилове за падащото меню */
            background-color: lightgray; /* 27. Светлосив фон */
            border: 2px solid gray; /* 28. Сива рамка */
            padding: 10px 20px; /* 29. Вътрешно отстояние (вертикално и хоризонтално) */
            border-radius: 5px; /* 30. Заобляне на ъглите */
            font-size: 16px; /* 31. Големина на шрифта */
            cursor: pointer; /* 32. Промяна на курсора при задържане (рука) */
        } /* 33. Край на .country-dropdown */

        .header { /* 34. Стил за заглавната част с времето */
            text-align: center; /* 35. Центрирано подравняване */
            margin-bottom: 20px; /* 36. Разстояние под хедъра */
        } /* 37. Край на .header */

        .location { /* 38. Стил за текста с местоположение */
            font-size: 18px; /* 39. Размер на шрифта */
            margin-bottom: 10px; /* 40. Отстояние под него */
        } /* 41. Край на .location */

        .temperature { /* 42. Стил за температурата */
            font-size: 48px; /* 43. Голям размер на шрифта */
            font-weight: bold; /* 44. Удебелен текст */
            color: blue; /* 45. Син цвят */
            margin-bottom: 10px; /* 46. Отстояние под температурата */
        } /* 47. Край на .temperature */

        .description { /* 48. Стил за описанието на времето */
            font-size: 16px; /* 49. Размер на шрифта */
            color: gray; /* 50. Сив цвят */
        } /* 51. Край на .description */

        .details { /* 52. Контейнер за допълнителни детайли (вятър, влажност) */
            display: flex; /* 53. Използва flexbox за подредба */
            justify-content: space-around; /* 54. Разпределя пространството равномерно */
            margin: 20px 0; /* 55. Горен и долен марж */
        } /* 56. Край на .details */

        .detail-box { /* 57. Стил за отделна кутия с детайл */
            text-align: center; /* 58. Центрира съдържанието */
            background-color: lightgray; /* 59. Светлосив фон */
            padding: 15px; /* 60. Вътрешно отстояние */
            border-radius: 5px; /* 61. Заобляне на ъглите */
            width: 45%; /* 62. Задава ширина */
        } /* 63. Край на .detail-box */

        .detail-title { /* 64. Стил за заглавие на детайла */
            font-size: 14px; /* 65. Малък шрифт */
            color: gray; /* 66. Сив цвят */
            margin-bottom: 5px; /* 67. Отстояние под заглавието */
        } /* 68. Край на .detail-title */

        .detail-value { /* 69. Стил за стойността на детайла */
            font-size: 20px; /* 70. По-голям шрифт */
            font-weight: bold; /* 71. Удебелен */
            color: black; /* 72. Черен текст */
        } /* 73. Край на .detail-value */

        .forecast { /* 74. Контейнер за прогнозата */
            margin-top: 20px; /* 75. Отстояние отгоре */
        } /* 76. Край на .forecast */

        .forecast-title { /* 77. Заглавие на прогнозата */
            font-size: 18px; /* 78. Размер на шрифта */
            font-weight: bold; /* 79. Удебелен */
            margin-bottom: 15px; /* 80. Отстояние под заглавието */
            text-align: center; /* 81. Центриране */
        } /* 82. Край на .forecast-title */

        .forecast-items { /* 83. Контейнер за елементите от прогнозата */
            display: grid; /* 84. Използва CSS grid */
            grid-template-columns: repeat(4, 1fr); /* 85. 4 еднакви колони */
            gap: 10px; /* 86. Разстояние между елементите */
        } /* 87. Край на .forecast-items */

        .forecast-item { /* 88. Всеки отделен елемент от прогнозата */
            text-align: center; /* 89. Центриран текст */
            background-color: lightgray; /* 90. Светлосив фон */
            padding: 8px; /* 91. Вътрешно отстояние */
            border-radius: 5px; /* 92. Заобляне на ъглите */
        } /* 93. Край на .forecast-item */

        .forecast-time { /* 94. Стил за часа във всеки блок */
            font-size: 11px; /* 95. Много малък шрифт */
            color: gray; /* 96. Сив текст */
            margin-bottom: 3px; /* 97. Малко разстояние под него */
        } /* 98. Край на .forecast-time */

        .forecast-temp { /* 99. Стил за температурата във всеки блок */
            font-size: 14px; /* 100. Среден шрифт */
            font-weight: bold; /* 101. Удебелен */
            color: black; /* 102. Черен */
        } /* 103. Край на .forecast-temp */

        @media (max-width: 480px) { /* 104. Медия заявка за малки екрани (мобилни) */
            .forecast-items { /* 105. Промяна на оформлението при мобилни */
                grid-template-columns: repeat(2, 1fr); /* 106. 2 колони вместо 4 */
            } /* 107. Край на модифицирания .forecast-items */
        } /* 108. Край на @media */
    </style> <!-- 109. Край на CSS стила -->
</head> <!-- 110. Край на head секцията -->
<body> <!-- 111. Начало на body секцията -->
    <div class="weather-app"> <!-- 112. Основен контейнер на приложението -->

        <div class="country-selector"> <!-- 113. Контейнер за избор на държава -->
            <select class="country-dropdown" onchange="showWeather(this.value)"> <!-- 114. Падащо меню, при промяна извиква функция -->
                <option value="bulgaria">Bulgaria</option> <!-- 115. Опция: България -->
                <option value="uk">United Kingdom</option> <!-- 116. Опция: Великобритания -->
            </select> <!-- 117. Край на падащото меню -->
        </div> <!-- 118. Край на country-selector -->

        <div class="header"> <!-- 119. Заглавна част (локейшън, температура, описание) -->
            <div class="location" id="location">Sofia, Bulgaria</div> <!-- 120. Показва текущото местоположение -->
            <div class="temperature" id="temperature">22°</div> <!-- 121. Показва текущата температура -->
            <div class="description" id="description">Sunny</div> <!-- 122. Показва описание на времето -->
        </div> <!-- 123. Край на header -->

        <div class="details"> <!-- 124. Секция за допълнителни детайли -->
            <div class="detail-box"> <!-- 125. Първа кутия: скорост на вятъра -->
                <div class="detail-title">Wind Speed</div> <!-- 126. Заглавие: скорост на вятъра -->
                <div class="detail-value" id="windSpeed">15 km/h</div> <!-- 127. Стойност: 15 km/h -->
            </div> <!-- 128. Край на първата detail-box -->
            <div class="detail-box"> <!-- 129. Втора кутия: влажност -->
                <div class="detail-title">Humidity</div> <!-- 130. Заглавие: влажност -->
                <div class="detail-value" id="humidity">5%</div> <!-- 131. Стойност: 5% -->
            </div> <!-- 132. Край на втората detail-box -->
        </div> <!-- 133. Край на секцията details -->

        <div class="forecast"> <!-- 134. Секция за прогнозата -->
            <div class="forecast-title">Today's Forecast (Every 3 Hours)</div> <!-- 135. Заглавие на прогнозата -->
            <div class="forecast-items" id="forecastItems"> <!-- 136. Контейнер, където JS ще постави елементите -->
                <!-- 137. Тук ще се генерират прогнози чрез JavaScript -->
            </div> <!-- 138. Край на forecast-items -->
        </div> <!-- 139. Край на forecast секцията -->

    </div> <!-- 140. Край на контейнера weather-app -->

    <script> <!-- 141. Начало на JavaScript код -->
        // 142. Декларира обект с твърдо зададени данни за времето
        var weatherData = {
            // 143. Данни за България
            bulgaria: {
                location: "Sofia, Bulgaria", // 144. Име на мястото
                temperature: "22°", // 145. Температура
                description: "Sunny", // 146. Описание
                windSpeed: "15 km/h", // 147. Скорост на вятъра
                humidity: "5%", // 148. Влажност
                forecast: [ // 149. Масив от прогнозни елементи на всеки 3 часа
                    {time: "00:00", temp: "15°"}, // 150. Прогноза за 00:00
                    {time: "03:00", temp: "12°"}, // 151. Прогноза за 03:00
                    {time: "06:00", temp: "14°"}, // 152. Прогноза за 06:00
                    {time: "09:00", temp: "20°"}, // 153. Прогноза за 09:00
                    {time: "12:00", temp: "26°"}, // 154. Прогноза за 12:00
                    {time: "15:00", temp: "27°"}, // 155. Прогноза за 15:00
                    {time: "18:00", temp: "23°"}, // 156. Прогноза за 18:00
                    {time: "21:00", temp: "19°"}  // 157. Прогноза за 21:00
                ]
            }, // 158. Край на обекта за България
            // 159. Данни за Обединеното кралство
            uk: {
                location: "London, UK", // 160. Местоположение
                temperature: "8°", // 161. Температура
                description: "Rainy", // 162. Описание
                windSpeed: "22 km/h", // 163. Вятър
                humidity: "85%", // 164. Влажност
                forecast: [ // 165. Прогноза на всеки 3 часа
                    {time: "00:00", temp: "6°"}, // 166. 00:00
                    {time: "03:00", temp: "4°"}, // 167. 03:00
                    {time: "06:00", temp: "5°"}, // 168. 06:00
                    {time: "09:00", temp: "8°"}, // 169. 09:00
                    {time: "12:00", temp: "11°"}, // 170. 12:00
                    {time: "15:00", temp: "11°"}, // 171. 15:00
                    {time: "18:00", temp: "8°"}, // 172. 18:00
                    {time: "21:00", temp: "7°"}  // 173. 21:00
                ]
            } // 174. Край на обекта за UK
        }; // 175. Край на weatherData обекта

        // 176. Функция, която обновява показаните данни според избраната държава
        function showWeather(country) {
            // 177. Взима данните за дадената страна от weatherData
            var data = weatherData[country];

            // 178. Обновява елемента с id "location" с текста за местоположение
            document.getElementById('location').textContent = data.location;
            // 179. Обновява температурата
            document.getElementById('temperature').textContent = data.temperature;
            // 180. Обновява описанието на времето
            document.getElementById('description').textContent = data.description;
            // 181. Обновява скоростта на вятъра
            document.getElementById('windSpeed').textContent = data.windSpeed;
            // 182. Обновява влажността
            document.getElementById('humidity').textContent = data.humidity;

            // 183. Взима контейнера за прогнозните елементи
            var forecastContainer = document.getElementById('forecastItems');
            // 184. Изчиства предишните прогнози (ако има такива)
            forecastContainer.innerHTML = '';

            // 185. Обхожда всеки елемент от forecast масива
            for (var i = 0; i < data.forecast.length; i++) {
                // 186. Създава нов div за всеки часови блок
                var item = document.createElement('div');
                // 187. Присвоява клас за стилизиране
                item.className = 'forecast-item';
                // 188. Задава вътрешното съдържание: време и температура
                item.innerHTML = '<div class="forecast-time">' + data.forecast[i].time + '</div><div class="forecast-temp">' + data.forecast[i].temp + '</div>';
                // 189. Добавя блока към контейнера
                forecastContainer.appendChild(item);
            } // 190. Край на for цикъла
        } // 191. Край на showWeather функцията

        // 192. Инициално извикване, за да зареди данните за България при отваряне
        showWeather('bulgaria');
    </script> <!-- 193. Край на JavaScript секцията -->
</body> <!-- 194. Край на body -->
</html> <!-- 195. Край на HTML документа -->
