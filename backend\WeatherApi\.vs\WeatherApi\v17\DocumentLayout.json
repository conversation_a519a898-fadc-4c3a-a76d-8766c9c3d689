{"Version": 1, "WorkspaceRootPath": "C:\\prj\\weather\\backend\\WeatherApi\\", "Documents": [{"AbsoluteMoniker": "D:0:0:{59B7DE80-3982-F0F9-E5D7-CAFE78C1D249}|WeatherApi.csproj|c:\\prj\\weather\\backend\\weatherapi\\weatherinfo.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{59B7DE80-3982-F0F9-E5D7-CAFE78C1D249}|WeatherApi.csproj|solutionrelative:weatherinfo.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{59B7DE80-3982-F0F9-E5D7-CAFE78C1D249}|WeatherApi.csproj|c:\\prj\\weather\\backend\\weatherapi\\program.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{59B7DE80-3982-F0F9-E5D7-CAFE78C1D249}|WeatherApi.csproj|solutionrelative:program.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{A2FE74E1-B743-11D0-AE1A-00A0C90FFFC3}|<MiscFiles>|C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\MSBuild\\Current\\Bin\\amd64\\Microsoft.Common.CurrentVersion.targets||{FA3CD31E-987B-443A-9B81-186104E8DAC1}|"}], "DocumentGroupContainers": [{"Orientation": 0, "VerticalTabListWidth": 256, "DocumentGroups": [{"DockedWidth": 200, "SelectedChildIndex": 2, "Children": [{"$type": "Document", "DocumentIndex": 2, "Title": "Microsoft.Common.CurrentVersion.targets", "DocumentMoniker": "C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\MSBuild\\Current\\Bin\\amd64\\Microsoft.Common.CurrentVersion.targets", "RelativeDocumentMoniker": "..\\..\\..\\..\\Program Files\\Microsoft Visual Studio\\2022\\Community\\MSBuild\\Current\\Bin\\amd64\\Microsoft.Common.CurrentVersion.targets", "ToolTip": "C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\MSBuild\\Current\\Bin\\amd64\\Microsoft.Common.CurrentVersion.targets", "RelativeToolTip": "..\\..\\..\\..\\Program Files\\Microsoft Visual Studio\\2022\\Community\\MSBuild\\Current\\Bin\\amd64\\Microsoft.Common.CurrentVersion.targets", "ViewState": "AgIAAAcVAAAAAAAAAAAawBEVAAAEAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.003801|", "WhenOpened": "2025-08-02T21:10:19.099Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 1, "Title": "Program.cs", "DocumentMoniker": "C:\\prj\\weather\\backend\\WeatherApi\\Program.cs", "RelativeDocumentMoniker": "Program.cs", "ToolTip": "C:\\prj\\weather\\backend\\WeatherApi\\Program.cs", "RelativeToolTip": "Program.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAADsAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-08-02T21:07:20.431Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 0, "Title": "WeatherInfo.cs", "DocumentMoniker": "C:\\prj\\weather\\backend\\WeatherApi\\WeatherInfo.cs", "RelativeDocumentMoniker": "WeatherInfo.cs", "ToolTip": "C:\\prj\\weather\\backend\\WeatherApi\\WeatherInfo.cs", "RelativeToolTip": "WeatherInfo.cs", "ViewState": "AgIAAA0AAAAAAAAAAAA1wBUAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-08-02T21:05:04.626Z", "EditorCaption": ""}]}]}]}