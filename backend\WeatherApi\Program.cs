﻿using Microsoft.AspNetCore.Builder;      // 1. Даваме достъп до WebApplication класа
using System.Collections.Generic;        // 2. За Dictionary и List

/* ---------- Създаваме builder за услугите ---------- */
var builder = WebApplication.CreateBuilder(args); // 3. Инициализира приложението

/* ----------  ДОБАВЯМЕ CORS (разрешаваме заявки от друг origin) ---------- */
builder.Services.AddCors(options =>                // 4. Регистр. CORS услуга
{
    options.AddDefaultPolicy(p =>                  // 5. Създаваме “Default” политика
        p.AllowAnyOrigin()                         // 6. Позволяваме всякакъв Origin (http://localhost:8000 и др.)
         .AllowAnyMethod()                         // 7. Позволяваме GET/POST/PUT … всякакви методи
         .AllowAnyHeader());                       // 8. Позволяваме всякакви HTTP заглавки
});

/* ---------- Изграждаме app-а ---------- */
var app = builder.Build();                         // 9. Създаваме final WebApplication

/* ---------- Включваме CORS middleware ---------- */
app.UseCors();                                     // 10. Заявките минават през CORS филтър

/* ---------- Тестови данни в паметта ---------- */
var weatherData = new Dictionary<string, WeatherInfo>(StringComparer.OrdinalIgnoreCase) // 11. Речник <държава, данни>
{
    ["bulgaria"] = new WeatherInfo         // 12. Ключ "bulgaria"
    {
        Location = "Sofia, Bulgaria",   // 13. Местоположение
        Temperature = "22°",               // 14. Температура
        Description = "Sunny",             // 15. Описание
        WindSpeed = "15 km/h",           // 16. Вятър
        Humidity = "5%",                // 17. Влажност
        Forecast = new()                   // 18. Списък с прогноза
        {
            new() { Time = "00:00", Temp = "15°" }, // 19. и т.н.
            new() { Time = "03:00", Temp = "12°" },
            new() { Time = "06:00", Temp = "14°" },
            new() { Time = "09:00", Temp = "20°" },
            new() { Time = "12:00", Temp = "26°" },
            new() { Time = "15:00", Temp = "27°" },
            new() { Time = "18:00", Temp = "23°" },
            new() { Time = "21:00", Temp = "19°" },
        }
    },

    ["uk"] = new WeatherInfo               // 20. Ключ "uk"
    {
        Location = "London, UK",
        Temperature = "8°",
        Description = "Rainy",
        WindSpeed = "22 km/h",
        Humidity = "85%",
        Forecast = new()
        {
            new() { Time = "00:00", Temp = "6°" },
            new() { Time = "03:00", Temp = "4°" },
            new() { Time = "06:00", Temp = "5°" },
            new() { Time = "09:00", Temp = "8°" },
            new() { Time = "12:00", Temp = "11°" },
            new() { Time = "15:00", Temp = "11°" },
            new() { Time = "18:00", Temp = "8°" },
            new() { Time = "21:00", Temp = "7°" },
        }
    }
};

/* ---------- Endpoint: /weather/{country} ---------- */
app.MapGet("/weather/{country}", (string country) =>        // 21. При GET заявка с параметър country
    weatherData.TryGetValue(country, out var info)          // 22. Проверяваме има ли данни
        ? Results.Ok(info)                                  // 23. Ако да → 200 OK + JSON
        : Results.NotFound(new { Message = "Country not found" }) // 24. Ако не → 404 NotFound
);

/* ---------- Стартираме приложението ---------- */
app.Run(); // 25. Започва да слуша на http://localhost:5240
